package main

import (
	"os/exec"
	"strings"
	"testing"

	"github.com/Laisky/errors/v2"
	"github.com/stretchr/testify/assert"
)

// TestZapLoggingCompliance verifies that our logging improvements work correctly
func TestZapLoggingCompliance(t *testing.T) {
	t.Run("NoStringConcatenationInLogging", func(t *testing.T) {
		// Search for problematic patterns in the codebase
		cmd := exec.Command("grep", "-r", "logger.*err\\.Error()", "--include=*.go", ".")
		output, err := cmd.Output()

		// We expect this to return no results (exit code 1 means no matches found)
		if err != nil {
			// grep returns exit code 1 when no matches are found, which is what we want
			if exitError, ok := err.(*exec.ExitError); ok && exitError.ExitCode() == 1 {
				// No matches found - this is good!
				return
			}
			// Some other error occurred
			t.Fatalf("Error running grep: %v", err)
		}

		// If we get here, grep found matches, which means we still have problematic patterns
		matches := strings.TrimSpace(string(output))
		if matches != "" {
			t.Errorf("Found logger calls with err.Error() string concatenation:\n%s", matches)
		}
	})

	t.Run("NoStringConcatenationPatterns", func(t *testing.T) {
		// Search for specific problematic patterns
		patterns := []string{
			"logger.*\".*: \" + err\\.Error()",
			"logger.*fmt\\.Sprintf.*err\\.Error()",
		}

		for _, pattern := range patterns {
			cmd := exec.Command("grep", "-r", "-E", pattern, "--include=*.go", ".")
			output, err := cmd.Output()

			if err != nil {
				// grep returns exit code 1 when no matches are found, which is what we want
				if exitError, ok := err.(*exec.ExitError); ok && exitError.ExitCode() == 1 {
					continue // No matches found - this is good!
				}
				t.Fatalf("Error running grep for pattern %s: %v", pattern, err)
			}

			matches := strings.TrimSpace(string(output))
			if matches != "" {
				t.Errorf("Found problematic logging pattern %s:\n%s", pattern, matches)
			}
		}
	})

	t.Run("ZapImportsPresent", func(t *testing.T) {
		// Verify that files that were modified have zap imports
		filesToCheck := []string{
			"model/token.go",
			"model/cost.go",
			"controller/relay.go",
			"controller/channel-test.go",
			"controller/auth/oidc.go",
			"controller/auth/github.go",
			"controller/auth/lark.go",
			"middleware/turnstile-check.go",
			"monitor/channel.go",
			"relay/adaptor/aws/llama3/main.go",
			"relay/adaptor/ollama/main.go",
			"relay/billing/ratio/group.go",
		}

		for _, file := range filesToCheck {
			cmd := exec.Command("grep", "-q", "github.com/Laisky/zap", file)
			err := cmd.Run()
			assert.NoError(t, err, "File %s should have zap import", file)
		}
	})
}

// TestBackwardCompatibility ensures that error messages in HTTP responses still work
func TestBackwardCompatibility(t *testing.T) {
	t.Run("ErrorMessagesInHTTPResponses", func(t *testing.T) {
		testErr := errors.New("invalid request format")

		// This pattern should still work for HTTP responses
		errorMessage := testErr.Error()
		assert.Equal(t, "invalid request format", errorMessage)

		// Verify that err.Error() still returns the expected string
		assert.IsType(t, "", errorMessage)
		assert.NotEmpty(t, errorMessage)
	})

	t.Run("ErrorWrappingStillWorks", func(t *testing.T) {
		originalErr := errors.New("original error")

		// Test that error wrapping patterns still work
		wrappedErr := errors.New("wrapped: " + originalErr.Error())
		assert.Contains(t, wrappedErr.Error(), "wrapped: original error")

		// But we should prefer errors.Wrap for this
		betterWrappedErr := errors.Wrap(originalErr, "wrapped")
		assert.Contains(t, betterWrappedErr.Error(), "wrapped")
		assert.Contains(t, betterWrappedErr.Error(), "original error")
	})
}
